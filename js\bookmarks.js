// ===== BOOKMARK MANAGER SYSTEM =====

class BookmarkManager {
    constructor() {
        this.bookmarks = this.loadBookmarks();
        this.folders = this.loadFolders();
        this.categories = this.loadCategories();
        this.currentEditingId = null;
        this.currentFilter = 'all';
        this.currentSort = 'recent';
        this.searchHistory = [];
        this.init();

        // Ensure bookmarks are saved to localStorage
        this.saveBookmarksToStorage();
        console.log('BookmarkManager initialized with', this.bookmarks.length, 'bookmarks');
    }

    init() {
        this.bindEvents();
        this.renderBookmarksInSearch();
        this.renderBookmarksList();
        this.updateStatistics();
        this.populateFolderDropdown();
        this.renderFoldersList();
    }

    bindEvents() {
        // Header bookmark button
        document.getElementById('bookmarkBtn')?.addEventListener('click', () => {
            this.openBookmarkModal();
        });

        // Search tab switching
        document.querySelectorAll('.search-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchSearchTab(e.target.dataset.tab);
            });
        });

        // Bookmark search
        document.getElementById('bookmarkSearchInput')?.addEventListener('input', (e) => {
            this.searchBookmarksInModal(e.target.value);
        });

        // Category filters
        document.querySelectorAll('.category-filter').forEach(filter => {
            filter.addEventListener('click', (e) => {
                this.filterByCategory(e.target.dataset.category);
            });
        });

        // Add bookmark button
        document.getElementById('addBookmarkBtn')?.addEventListener('click', () => {
            this.openBookmarkModal();
        });

        // Manage bookmarks button
        document.getElementById('manageBookmarksBtn')?.addEventListener('click', () => {
            this.openBookmarkModal();
        });

        // Bookmark form
        document.getElementById('bookmarkForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveBookmark();
        });

        // Cancel button
        document.getElementById('cancelBookmarkBtn')?.addEventListener('click', () => {
            this.resetBookmarkForm();
        });

        // Delete button
        document.getElementById('deleteBookmarkBtn')?.addEventListener('click', () => {
            this.deleteBookmark();
        });

        // Import bookmarks (keeping only the file input for potential future use)
        document.getElementById('importBookmarksFile')?.addEventListener('change', (e) => {
            this.importBookmarks(e.target.files[0]);
        });

        // Clear search
        document.getElementById('clearBookmarkSearchBtn')?.addEventListener('click', () => {
            document.getElementById('bookmarkSearchInput').value = '';
            this.renderBookmarksInSearch();
        });

        // Bookmark list search (real-time search in bookmark manager)
        document.getElementById('bookmarkListSearchInput')?.addEventListener('input', (e) => {
            this.searchBookmarksList(e.target.value);
        });

        // Clear bookmark list search
        document.getElementById('clearBookmarkListSearchBtn')?.addEventListener('click', () => {
            this.clearBookmarkListSearch();
        });

        // Sort and filter events
        document.getElementById('bookmarkSortSelect')?.addEventListener('change', (e) => {
            this.currentSort = e.target.value;
            this.renderBookmarksInSearch();
        });

        document.querySelectorAll('.quick-filter').forEach(filter => {
            filter.addEventListener('click', (e) => {
                this.setQuickFilter(e.target.dataset.filter);
            });
        });

        // Advanced options
        document.getElementById('checkBrokenLinksBtn')?.addEventListener('click', () => {
            this.checkAllBrokenLinks();
        });

        document.getElementById('findDuplicatesBtn')?.addEventListener('click', () => {
            this.showDuplicates();
        });

        document.getElementById('updateMetadataBtn')?.addEventListener('click', () => {
            this.updateAllMetadata();
        });

        // Folder management
        document.getElementById('addFolderBtn')?.addEventListener('click', () => {
            this.showFolderDialog();
        });

        document.getElementById('manageFoldersBtn')?.addEventListener('click', () => {
            this.toggleFolderManagement();
        });

        // Fullscreen button for bookmark modal
        document.getElementById('bookmarkFullscreenBtn')?.addEventListener('click', () => {
            this.toggleBookmarkFullscreen();
        });
    }

    // Load bookmarks from localStorage
    loadBookmarks() {
        try {
            const saved = localStorage.getItem('smartNoteBookmarks');
            const bookmarks = saved ? JSON.parse(saved) : [];

            // Add default bookmarks if none exist
            if (bookmarks.length === 0) {
                return this.getDefaultBookmarks();
            }

            return bookmarks;
        } catch (error) {
            console.error('Error loading bookmarks:', error);
            return this.getDefaultBookmarks();
        }
    }

    // Get default bookmarks
    getDefaultBookmarks() {
        return [
            {
                id: 'default-1',
                title: 'Google',
                url: 'https://www.google.com',
                category: 'tools',
                folder: null,
                tags: ['search', 'google'],
                description: 'বিশ্বের সবচেয়ে জনপ্রিয় সার্চ ইঞ্জিন',
                favicon: 'https://www.google.com/s2/favicons?domain=google.com&sz=32',
                isFavorite: false,
                visitCount: 0,
                lastVisited: null,
                isWorking: true,
                screenshot: null,
                metaData: null,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            },
            {
                id: 'default-2',
                title: 'YouTube',
                url: 'https://www.youtube.com',
                category: 'entertainment',
                folder: null,
                tags: ['video', 'entertainment'],
                description: 'ভিডিও দেখার জন্য সবচেয়ে বড় প্ল্যাটফর্ম',
                favicon: 'https://www.google.com/s2/favicons?domain=youtube.com&sz=32',
                isFavorite: true,
                visitCount: 5,
                lastVisited: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
                isWorking: true,
                screenshot: null,
                metaData: null,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            },
            {
                id: 'default-3',
                title: 'GitHub',
                url: 'https://github.com',
                category: 'work',
                folder: null,
                tags: ['code', 'development', 'git'],
                description: 'কোড হোস্টিং এবং ভার্সন কন্ট্রোল',
                favicon: 'https://www.google.com/s2/favicons?domain=github.com&sz=32',
                isFavorite: false,
                visitCount: 12,
                lastVisited: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
                isWorking: true,
                screenshot: null,
                metaData: null,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            },
            {
                id: 'default-4',
                title: 'Facebook',
                url: 'https://www.facebook.com',
                category: 'social',
                folder: null,
                tags: ['social', 'facebook'],
                description: 'সামাজিক নেটওয়ার্কিং সাইট',
                favicon: 'https://www.google.com/s2/favicons?domain=facebook.com&sz=32',
                isFavorite: true,
                visitCount: 8,
                lastVisited: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
                isWorking: true,
                screenshot: null,
                metaData: null,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            },
            {
                id: 'default-5',
                title: 'প্রথম আলো',
                url: 'https://www.prothomalo.com',
                category: 'news',
                folder: null,
                tags: ['news', 'bangla', 'bangladesh'],
                description: 'বাংলাদেশের জনপ্রিয় সংবাদপত্র',
                favicon: 'https://www.google.com/s2/favicons?domain=prothomalo.com&sz=32',
                isFavorite: false,
                visitCount: 3,
                lastVisited: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
                isWorking: true,
                screenshot: null,
                metaData: null,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            }
        ];
    }

    // Load folders from localStorage
    loadFolders() {
        try {
            const saved = localStorage.getItem('smartNoteBookmarkFolders');
            const folders = saved ? JSON.parse(saved) : [];

            // Add default folders if none exist
            if (folders.length === 0) {
                return this.getDefaultFolders();
            }

            return folders;
        } catch (error) {
            console.error('Error loading folders:', error);
            return this.getDefaultFolders();
        }
    }

    // Get default folders
    getDefaultFolders() {
        return [
            {
                id: 'folder-1',
                name: 'দৈনন্দিন',
                description: 'প্রতিদিনের প্রয়োজনীয় সাইট',
                color: '#4a6bdf',
                createdAt: new Date().toISOString()
            },
            {
                id: 'folder-2',
                name: 'প্রোগ্রামিং',
                description: 'কোডিং রিসোর্স',
                color: '#28a745',
                createdAt: new Date().toISOString()
            },
            {
                id: 'folder-3',
                name: 'বিনোদন',
                description: 'মজার সাইট',
                color: '#fd7e14',
                createdAt: new Date().toISOString()
            }
        ];
    }

    // Load categories from localStorage
    loadCategories() {
        try {
            const saved = localStorage.getItem('smartNoteBookmarkCategories');
            const categories = saved ? JSON.parse(saved) : [];

            // Add default categories if none exist
            if (categories.length === 0) {
                return this.getDefaultCategories();
            }

            return categories;
        } catch (error) {
            console.error('Error loading categories:', error);
            return this.getDefaultCategories();
        }
    }

    // Get default categories
    getDefaultCategories() {
        return [
            { id: 'work', name: 'কাজ', icon: 'fas fa-briefcase', color: '#4a6bdf' },
            { id: 'education', name: 'শিক্ষা', icon: 'fas fa-graduation-cap', color: '#28a745' },
            { id: 'entertainment', name: 'বিনোদন', icon: 'fas fa-gamepad', color: '#fd7e14' },
            { id: 'social', name: 'সামাজিক', icon: 'fas fa-users', color: '#6f42c1' },
            { id: 'tools', name: 'টুলস', icon: 'fas fa-tools', color: '#20c997' },
            { id: 'news', name: 'সংবাদ', icon: 'fas fa-newspaper', color: '#dc3545' },
            { id: 'other', name: 'অন্যান্য', icon: 'fas fa-folder', color: '#6c757d' }
        ];
    }

    // Save bookmarks to localStorage
    saveBookmarksToStorage() {
        try {
            localStorage.setItem('smartNoteBookmarks', JSON.stringify(this.bookmarks));
        } catch (error) {
            console.error('Error saving bookmarks:', error);
        }
    }

    // Save folders to localStorage
    saveFoldersToStorage() {
        try {
            localStorage.setItem('smartNoteBookmarkFolders', JSON.stringify(this.folders));
        } catch (error) {
            console.error('Error saving folders:', error);
        }
    }

    // Save categories to localStorage
    saveCategoriesToStorage() {
        try {
            localStorage.setItem('smartNoteBookmarkCategories', JSON.stringify(this.categories));
        } catch (error) {
            console.error('Error saving categories:', error);
        }
    }

    // Generate unique ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // Get favicon URL
    getFaviconUrl(url) {
        try {
            const domain = new URL(url).hostname;
            return `https://www.google.com/s2/favicons?domain=${domain}&sz=32`;
        } catch {
            return null;
        }
    }

    // Switch search tabs
    switchSearchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.search-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.search-tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}SearchTab`).classList.add('active');

        // If switching to bookmarks tab, render bookmarks
        if (tabName === 'bookmarks') {
            this.renderBookmarksInSearch();
        }
    }

    // Search bookmarks in modal
    searchBookmarksInModal(query) {
        const filtered = this.filterBookmarks(query);
        this.renderBookmarksInSearch(filtered);
        
        // Show/hide clear button
        const clearBtn = document.getElementById('clearBookmarkSearchBtn');
        if (clearBtn) {
            clearBtn.style.display = query ? 'block' : 'none';
        }
    }

    // Filter bookmarks
    filterBookmarks(query = '', category = 'all') {
        return this.bookmarks.filter(bookmark => {
            const matchesQuery = !query || 
                bookmark.title.toLowerCase().includes(query.toLowerCase()) ||
                bookmark.url.toLowerCase().includes(query.toLowerCase()) ||
                bookmark.description.toLowerCase().includes(query.toLowerCase()) ||
                bookmark.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()));

            const matchesCategory = category === 'all' || bookmark.category === category;

            return matchesQuery && matchesCategory;
        });
    }

    // Filter by category
    filterByCategory(category) {
        // Update active filter
        document.querySelectorAll('.category-filter').forEach(filter => {
            filter.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`).classList.add('active');

        // Filter and render
        const query = document.getElementById('bookmarkSearchInput')?.value || '';
        const filtered = this.filterBookmarks(query, category);
        this.renderBookmarksInSearch(filtered);
    }

    // Render bookmarks in search modal
    renderBookmarksInSearch(bookmarks = null) {
        const container = document.getElementById('bookmarkResults');
        const statsContainer = document.getElementById('bookmarkStats');

        if (!container) return;

        // Use filtered bookmarks if no specific bookmarks provided
        if (!bookmarks) {
            bookmarks = this.getFilteredBookmarks();
        }

        if (bookmarks.length === 0) {
            container.innerHTML = `
                <div class="bookmark-empty-state">
                    <i class="fas fa-bookmark"></i>
                    <h4>কোন বুকমার্ক পাওয়া যায়নি</h4>
                    <p>নতুন বুকমার্ক যোগ করুন অথবা সার্চ টার্ম পরিবর্তন করুন</p>
                </div>
            `;
            if (statsContainer) statsContainer.style.display = 'none';
            return;
        }

        container.innerHTML = bookmarks.map(bookmark => `
            <div class="bookmark-item ${bookmark.isFavorite ? 'favorite' : ''}" data-id="${bookmark.id}">
                <div class="bookmark-favicon">
                    ${bookmark.favicon ?
                        `<img src="${bookmark.favicon}" alt="favicon" onerror="this.style.display='none'">` :
                        bookmark.title.charAt(0).toUpperCase()
                    }
                </div>
                <div class="bookmark-content">
                    <div class="bookmark-title">${this.escapeHtml(bookmark.title)}</div>
                    <div class="bookmark-url">${this.escapeHtml(bookmark.url)}</div>
                    <div class="bookmark-meta">
                        <span class="bookmark-category">${this.getCategoryName(bookmark.category)}</span>
                        <div class="bookmark-tags">
                            ${bookmark.tags.map(tag => `<span class="bookmark-tag">${this.escapeHtml(tag)}</span>`).join('')}
                        </div>
                    </div>
                    <div class="bookmark-visit-info">
                        ${bookmark.visitCount > 0 ? `<span class="visit-count">${bookmark.visitCount} বার ভিজিট</span>` : ''}
                        ${bookmark.lastVisited ? `<span class="last-visited">শেষ ভিজিট: ${this.formatDate(bookmark.lastVisited)}</span>` : ''}
                    </div>
                    <div class="bookmark-status">
                        <span class="status-indicator ${bookmark.isWorking === false ? 'broken' : bookmark.isWorking === true ? 'working' : 'checking'}"></span>
                        <span class="status-text">${bookmark.isWorking === false ? 'ব্রোকেন লিংক' : bookmark.isWorking === true ? 'কার্যকর' : 'চেক করা হয়নি'}</span>
                    </div>
                </div>
                <div class="bookmark-actions-menu">
                    <button class="bookmark-action-btn favorite ${bookmark.isFavorite ? 'active' : ''}" onclick="bookmarkManager.toggleFavorite('${bookmark.id}')" title="ফেভরিট">
                        <i class="fas fa-star"></i>
                    </button>
                    <button class="bookmark-action-btn visit" onclick="bookmarkManager.visitBookmark('${bookmark.id}')" title="ভিজিট করুন">
                        <i class="fas fa-external-link-alt"></i>
                    </button>
                    <button class="bookmark-action-btn edit" onclick="bookmarkManager.editBookmark('${bookmark.id}')" title="সম্পাদনা">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="bookmark-action-btn delete" onclick="bookmarkManager.confirmDeleteBookmark('${bookmark.id}')" title="মুছে ফেলুন">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');

        // Update stats
        if (statsContainer) {
            statsContainer.innerHTML = `<span>${bookmarks.length} টি বুকমার্ক</span>`;
            statsContainer.style.display = 'block';
        }

        // Add click events for visiting bookmarks
        container.querySelectorAll('.bookmark-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (!e.target.closest('.bookmark-actions-menu')) {
                    const id = item.dataset.id;
                    this.visitBookmark(id);
                }
            });
        });
    }

    // Visit bookmark
    visitBookmark(id) {
        const bookmark = this.bookmarks.find(b => b.id === id);
        if (bookmark) {
            // Update visit count and last visited
            bookmark.visitCount = (bookmark.visitCount || 0) + 1;
            bookmark.lastVisited = new Date().toISOString();

            // Save updated data
            this.saveBookmarksToStorage();

            // Re-render if needed
            this.renderBookmarksInSearch();
            this.renderBookmarksList();

            // Open the URL
            window.open(bookmark.url, '_blank');

            // Show notification
            this.showNotification(`${bookmark.title} ভিজিট করা হয়েছে`, 'info');
        }
    }

    // Toggle favorite status
    toggleFavorite(id) {
        const bookmark = this.bookmarks.find(b => b.id === id);
        if (bookmark) {
            bookmark.isFavorite = !bookmark.isFavorite;
            bookmark.updatedAt = new Date().toISOString();

            this.saveBookmarksToStorage();
            this.renderBookmarksInSearch();
            this.renderBookmarksList();

            const status = bookmark.isFavorite ? 'ফেভরিটে যোগ' : 'ফেভরিট থেকে সরানো';
            this.showNotification(`${bookmark.title} ${status} হয়েছে`, 'success');
        }
    }

    // Get popular bookmarks (most visited)
    getPopularBookmarks(limit = 5) {
        return [...this.bookmarks]
            .sort((a, b) => (b.visitCount || 0) - (a.visitCount || 0))
            .slice(0, limit);
    }

    // Get recent bookmarks (recently added)
    getRecentBookmarks(limit = 5) {
        return [...this.bookmarks]
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, limit);
    }

    // Get favorite bookmarks
    getFavoriteBookmarks() {
        return this.bookmarks.filter(b => b.isFavorite);
    }

    // Get recently visited bookmarks
    getRecentlyVisited(limit = 5) {
        return [...this.bookmarks]
            .filter(b => b.lastVisited)
            .sort((a, b) => new Date(b.lastVisited) - new Date(a.lastVisited))
            .slice(0, limit);
    }

    // Check for duplicate bookmarks
    findDuplicates() {
        const duplicates = [];
        const urlMap = new Map();

        this.bookmarks.forEach(bookmark => {
            const normalizedUrl = this.normalizeUrl(bookmark.url);
            if (urlMap.has(normalizedUrl)) {
                duplicates.push({
                    original: urlMap.get(normalizedUrl),
                    duplicate: bookmark
                });
            } else {
                urlMap.set(normalizedUrl, bookmark);
            }
        });

        return duplicates;
    }

    // Normalize URL for duplicate detection
    normalizeUrl(url) {
        try {
            const urlObj = new URL(url);
            // Remove www, trailing slash, and convert to lowercase
            return urlObj.hostname.replace(/^www\./, '') + urlObj.pathname.replace(/\/$/, '');
        } catch {
            return url.toLowerCase();
        }
    }

    // Fetch metadata from URL
    async fetchMetadata(url) {
        try {
            // Use a CORS proxy or API to fetch metadata
            const proxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(url)}`;
            const response = await fetch(proxyUrl);
            const data = await response.json();

            if (data.contents) {
                const parser = new DOMParser();
                const doc = parser.parseFromString(data.contents, 'text/html');

                return {
                    title: doc.querySelector('title')?.textContent || '',
                    description: doc.querySelector('meta[name="description"]')?.content ||
                               doc.querySelector('meta[property="og:description"]')?.content || '',
                    image: doc.querySelector('meta[property="og:image"]')?.content || '',
                    siteName: doc.querySelector('meta[property="og:site_name"]')?.content || '',
                    keywords: doc.querySelector('meta[name="keywords"]')?.content || ''
                };
            }
        } catch (error) {
            console.error('Error fetching metadata:', error);
        }
        return null;
    }

    // Check if URL is working
    async checkUrlStatus(url) {
        try {
            const proxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(url)}`;
            const response = await fetch(proxyUrl, { method: 'HEAD' });
            return response.ok;
        } catch (error) {
            console.error('Error checking URL status:', error);
            return false;
        }
    }

    // Auto-complete search suggestions
    getSearchSuggestions(query) {
        if (!query || query.length < 2) return [];

        const suggestions = new Set();
        const queryLower = query.toLowerCase();

        this.bookmarks.forEach(bookmark => {
            // Add title matches
            if (bookmark.title.toLowerCase().includes(queryLower)) {
                suggestions.add(bookmark.title);
            }

            // Add tag matches
            bookmark.tags.forEach(tag => {
                if (tag.toLowerCase().includes(queryLower)) {
                    suggestions.add(tag);
                }
            });

            // Add domain matches
            try {
                const domain = new URL(bookmark.url).hostname;
                if (domain.toLowerCase().includes(queryLower)) {
                    suggestions.add(domain);
                }
            } catch {}
        });

        return Array.from(suggestions).slice(0, 5);
    }

    // Fuzzy search implementation
    fuzzySearch(query, text) {
        if (!query || !text) return false;

        const queryLower = query.toLowerCase();
        const textLower = text.toLowerCase();

        // Exact match
        if (textLower.includes(queryLower)) return true;

        // Character-by-character fuzzy matching
        let queryIndex = 0;
        for (let i = 0; i < textLower.length && queryIndex < queryLower.length; i++) {
            if (textLower[i] === queryLower[queryIndex]) {
                queryIndex++;
            }
        }

        return queryIndex === queryLower.length;
    }

    // Set quick filter
    setQuickFilter(filter) {
        // Update active filter button
        document.querySelectorAll('.quick-filter').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

        this.currentFilter = filter;
        this.renderBookmarksInSearch();
    }

    // Get filtered bookmarks based on current filter
    getFilteredBookmarks() {
        let filtered = [...this.bookmarks];

        // Apply quick filter
        switch (this.currentFilter) {
            case 'favorites':
                filtered = this.getFavoriteBookmarks();
                break;
            case 'popular':
                filtered = this.getPopularBookmarks(10);
                break;
            case 'recent':
                filtered = this.getRecentBookmarks(10);
                break;
            case 'recentlyVisited':
                filtered = this.getRecentlyVisited(10);
                break;
            default:
                // 'all' - no additional filtering
                break;
        }

        // Apply sorting
        switch (this.currentSort) {
            case 'popular':
                filtered.sort((a, b) => (b.visitCount || 0) - (a.visitCount || 0));
                break;
            case 'alphabetical':
                filtered.sort((a, b) => a.title.localeCompare(b.title));
                break;
            case 'category':
                filtered.sort((a, b) => a.category.localeCompare(b.category));
                break;
            case 'lastVisited':
                filtered.sort((a, b) => {
                    if (!a.lastVisited && !b.lastVisited) return 0;
                    if (!a.lastVisited) return 1;
                    if (!b.lastVisited) return -1;
                    return new Date(b.lastVisited) - new Date(a.lastVisited);
                });
                break;
            case 'visitCount':
                filtered.sort((a, b) => (b.visitCount || 0) - (a.visitCount || 0));
                break;
            default: // 'recent'
                filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
                break;
        }

        return filtered;
    }

    // Check all broken links
    async checkAllBrokenLinks() {
        this.showNotification('ব্রোকেন লিংক চেক করা হচ্ছে...', 'info');

        let brokenCount = 0;
        const promises = this.bookmarks.map(async (bookmark) => {
            const isWorking = await this.checkUrlStatus(bookmark.url);
            bookmark.isWorking = isWorking;
            if (!isWorking) brokenCount++;
        });

        await Promise.all(promises);

        this.saveBookmarksToStorage();
        this.renderBookmarksInSearch();
        this.updateStatistics();

        this.showNotification(`${brokenCount} টি ব্রোকেন লিংক পাওয়া গেছে`, brokenCount > 0 ? 'warning' : 'success');
    }

    // Show duplicates
    showDuplicates() {
        const duplicates = this.findDuplicates();

        if (duplicates.length === 0) {
            this.showNotification('কোন ডুপ্লিকেট বুকমার্ক পাওয়া যায়নি', 'success');
            return;
        }

        let message = `${duplicates.length} টি ডুপ্লিকেট বুকমার্ক পাওয়া গেছে:\n\n`;
        duplicates.forEach((dup, index) => {
            message += `${index + 1}. ${dup.original.title} এবং ${dup.duplicate.title}\n`;
        });

        if (confirm(message + '\nডুপ্লিকেট বুকমার্কগুলো মুছে ফেলতে চান?')) {
            duplicates.forEach(dup => {
                this.deleteBookmarkById(dup.duplicate.id);
            });
            this.showNotification('ডুপ্লিকেট বুকমার্ক মুছে ফেলা হয়েছে', 'success');
        }
    }

    // Update all metadata
    async updateAllMetadata() {
        this.showNotification('মেটাডেটা আপডেট করা হচ্ছে...', 'info');

        let updatedCount = 0;
        for (const bookmark of this.bookmarks) {
            const metadata = await this.fetchMetadata(bookmark.url);
            if (metadata) {
                bookmark.metaData = metadata;
                if (!bookmark.description && metadata.description) {
                    bookmark.description = metadata.description;
                }
                updatedCount++;
            }
        }

        this.saveBookmarksToStorage();
        this.renderBookmarksInSearch();

        this.showNotification(`${updatedCount} টি বুকমার্কের মেটাডেটা আপডেট হয়েছে`, 'success');
    }

    // Toggle bookmark fullscreen
    toggleBookmarkFullscreen() {
        const modal = document.getElementById('bookmarkModal');
        const fullscreenBtn = document.getElementById('bookmarkFullscreenBtn');

        if (!modal || !fullscreenBtn) return;

        const isFullscreen = modal.classList.contains('fullscreen');

        if (isFullscreen) {
            // Exit fullscreen
            modal.classList.remove('fullscreen');
            fullscreenBtn.classList.remove('active');
            fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
            fullscreenBtn.title = 'ফুল স্ক্রিন';
        } else {
            // Enter fullscreen
            modal.classList.add('fullscreen');
            fullscreenBtn.classList.add('active');
            fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
            fullscreenBtn.title = 'ফুল স্ক্রিন থেকে বের হন';
        }
    }

    // Update statistics
    updateStatistics() {
        const totalCount = this.bookmarks.length;
        const favoriteCount = this.getFavoriteBookmarks().length;
        const totalVisits = this.bookmarks.reduce((sum, b) => sum + (b.visitCount || 0), 0);
        const brokenCount = this.bookmarks.filter(b => b.isWorking === false).length;

        const totalEl = document.getElementById('totalBookmarksCount');
        const favoriteEl = document.getElementById('favoriteBookmarksCount');
        const visitsEl = document.getElementById('totalVisitsCount');
        const brokenEl = document.getElementById('brokenLinksCount');

        if (totalEl) totalEl.textContent = totalCount;
        if (favoriteEl) favoriteEl.textContent = favoriteCount;
        if (visitsEl) visitsEl.textContent = totalVisits;
        if (brokenEl) brokenEl.textContent = brokenCount;
    }

    // Format date for display
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) return 'এখনই';
        if (diffMins < 60) return `${diffMins} মিনিট আগে`;
        if (diffHours < 24) return `${diffHours} ঘন্টা আগে`;
        if (diffDays < 7) return `${diffDays} দিন আগে`;

        return date.toLocaleDateString('bn-BD');
    }

    // Populate folder dropdown
    populateFolderDropdown() {
        const folderSelect = document.getElementById('bookmarkFolder');
        if (!folderSelect) return;

        folderSelect.innerHTML = '<option value="">কোন ফোল্ডার নেই</option>';

        this.folders.forEach(folder => {
            const option = document.createElement('option');
            option.value = folder.id;
            option.textContent = folder.name;
            folderSelect.appendChild(option);
        });
    }

    // Show folder dialog
    showFolderDialog() {
        const name = prompt('ফোল্ডারের নাম দিন:');
        if (!name) return;

        const description = prompt('ফোল্ডারের বিবরণ দিন (ঐচ্ছিক):') || '';
        const colors = ['#4a6bdf', '#28a745', '#fd7e14', '#6f42c1', '#20c997', '#dc3545', '#6c757d'];
        const color = colors[Math.floor(Math.random() * colors.length)];

        const folder = {
            id: this.generateId(),
            name,
            description,
            color,
            createdAt: new Date().toISOString()
        };

        this.folders.push(folder);
        this.saveFoldersToStorage();
        this.populateFolderDropdown();
        this.renderFoldersList();
        this.showNotification('নতুন ফোল্ডার তৈরি হয়েছে', 'success');
    }

    // Toggle folder management mode
    toggleFolderManagement() {
        const foldersList = document.getElementById('foldersList');
        if (!foldersList) return;

        const isManaging = foldersList.classList.contains('managing');

        if (isManaging) {
            foldersList.classList.remove('managing');
            document.getElementById('manageFoldersBtn').innerHTML = '<i class="fas fa-cog"></i> সম্পাদনা';
        } else {
            foldersList.classList.add('managing');
            document.getElementById('manageFoldersBtn').innerHTML = '<i class="fas fa-check"></i> সম্পন্ন';
        }

        this.renderFoldersList();
    }

    // Edit folder
    editFolder(folderId) {
        const folder = this.folders.find(f => f.id === folderId);
        if (!folder) return;

        const newName = prompt('ফোল্ডারের নাম পরিবর্তন করুন:', folder.name);
        if (!newName || newName === folder.name) return;

        const newDescription = prompt('ফোল্ডারের বিবরণ পরিবর্তন করুন:', folder.description || '');

        folder.name = newName;
        folder.description = newDescription || '';
        folder.updatedAt = new Date().toISOString();

        this.saveFoldersToStorage();
        this.populateFolderDropdown();
        this.renderFoldersList();
        this.showNotification('ফোল্ডার আপডেট হয়েছে', 'success');
    }

    // Delete folder
    deleteFolder(folderId) {
        const folder = this.folders.find(f => f.id === folderId);
        if (!folder) return;

        // Check if any bookmarks are using this folder
        const bookmarksInFolder = this.bookmarks.filter(b => b.folder === folderId);

        let confirmMessage = `"${folder.name}" ফোল্ডারটি মুছে ফেলতে চান?`;
        if (bookmarksInFolder.length > 0) {
            confirmMessage += `\n\nএই ফোল্ডারে ${bookmarksInFolder.length} টি বুকমার্ক আছে। ফোল্ডার মুছে ফেললে বুকমার্কগুলো "কোন ফোল্ডার নেই" তে চলে যাবে।`;
        }

        if (!confirm(confirmMessage)) return;

        // Remove folder from bookmarks
        bookmarksInFolder.forEach(bookmark => {
            bookmark.folder = null;
        });

        // Remove folder
        this.folders = this.folders.filter(f => f.id !== folderId);

        this.saveFoldersToStorage();
        this.saveBookmarksToStorage();
        this.populateFolderDropdown();
        this.renderFoldersList();
        this.renderBookmarksInSearch();
        this.renderBookmarksList();

        this.showNotification('ফোল্ডার মুছে ফেলা হয়েছে', 'success');
    }

    // Render folders list
    renderFoldersList() {
        const container = document.getElementById('foldersList');
        if (!container) return;

        const isManaging = container.classList.contains('managing');

        if (this.folders.length === 0) {
            container.innerHTML = `
                <div class="folder-empty-state">
                    <i class="fas fa-folder-open"></i>
                    <p>কোন ফোল্ডার নেই</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.folders.map(folder => {
            const bookmarkCount = this.bookmarks.filter(b => b.folder === folder.id).length;

            return `
                <div class="folder-item ${isManaging ? 'managing' : ''}" data-id="${folder.id}">
                    <div class="folder-color" style="background-color: ${folder.color}"></div>
                    <div class="folder-name">${this.escapeHtml(folder.name)}</div>
                    <div class="folder-description">${this.escapeHtml(folder.description || '')}</div>
                    <div class="folder-count">${bookmarkCount} টি বুকমার্ক</div>
                    ${isManaging ? `
                        <div class="folder-actions">
                            <button class="folder-action-btn edit" onclick="bookmarkManager.editFolder('${folder.id}')" title="সম্পাদনা">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="folder-action-btn delete" onclick="bookmarkManager.deleteFolder('${folder.id}')" title="মুছে ফেলুন">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');
    }

    // Edit bookmark
    editBookmark(id) {
        const bookmark = this.bookmarks.find(b => b.id === id);
        if (bookmark) {
            this.currentEditingId = id;
            this.fillBookmarkForm(bookmark);
            this.openBookmarkModal();
        }
    }

    // Confirm delete bookmark
    confirmDeleteBookmark(id) {
        const bookmark = this.bookmarks.find(b => b.id === id);
        if (bookmark && confirm(`"${bookmark.title}" বুকমার্কটি মুছে ফেলতে চান?`)) {
            this.deleteBookmarkById(id);
        }
    }

    // Delete bookmark by ID
    deleteBookmarkById(id) {
        this.bookmarks = this.bookmarks.filter(b => b.id !== id);
        this.saveBookmarksToStorage();
        this.renderBookmarksInSearch();
        this.renderBookmarksList();
        this.showNotification('বুকমার্ক মুছে ফেলা হয়েছে', 'success');
    }

    // Get category name in Bengali
    getCategoryName(category) {
        const names = {
            work: 'কাজ',
            education: 'শিক্ষা',
            entertainment: 'বিনোদন',
            social: 'সামাজিক',
            tools: 'টুলস',
            news: 'সংবাদ',
            other: 'অন্যান্য'
        };
        return names[category] || category;
    }

    // Escape HTML
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Show notification
    showNotification(message, type = 'info') {
        // Use existing notification system if available
        if (window.showNotification) {
            window.showNotification(message, type);
        } else {
            alert(message);
        }
    }

    // Open bookmark modal
    openBookmarkModal() {
        const modal = document.getElementById('bookmarkModal');
        if (modal) {
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
    }

    // Close bookmark modal
    closeBookmarkModal() {
        const modal = document.getElementById('bookmarkModal');
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = '';
            this.resetBookmarkForm();
        }
    }

    // Fill bookmark form
    fillBookmarkForm(bookmark) {
        document.getElementById('bookmarkTitle').value = bookmark.title;
        document.getElementById('bookmarkUrl').value = bookmark.url;
        document.getElementById('bookmarkCategory').value = bookmark.category;
        document.getElementById('bookmarkFolder').value = bookmark.folder || '';
        document.getElementById('bookmarkFavorite').checked = bookmark.isFavorite || false;
        document.getElementById('bookmarkTags').value = bookmark.tags.join(', ');
        document.getElementById('bookmarkDescription').value = bookmark.description || '';

        // Show delete button
        document.getElementById('deleteBookmarkBtn').style.display = 'inline-flex';
    }

    // Reset bookmark form
    resetBookmarkForm() {
        document.getElementById('bookmarkForm').reset();
        document.getElementById('deleteBookmarkBtn').style.display = 'none';
        this.currentEditingId = null;
    }

    // Save bookmark
    saveBookmark() {
        const title = document.getElementById('bookmarkTitle').value.trim();
        const url = document.getElementById('bookmarkUrl').value.trim();
        const category = document.getElementById('bookmarkCategory').value;
        const folder = document.getElementById('bookmarkFolder').value || null;
        const isFavorite = document.getElementById('bookmarkFavorite').checked;
        const tags = document.getElementById('bookmarkTags').value
            .split(',')
            .map(tag => tag.trim())
            .filter(tag => tag);
        const description = document.getElementById('bookmarkDescription').value.trim();

        if (!title || !url) {
            alert('শিরোনাম এবং URL আবশ্যক!');
            return;
        }

        // Validate URL
        try {
            new URL(url);
        } catch {
            alert('সঠিক URL দিন!');
            return;
        }

        const existingBookmark = this.currentEditingId ?
            this.bookmarks.find(b => b.id === this.currentEditingId) : null;

        const bookmark = {
            id: this.currentEditingId || this.generateId(),
            title,
            url,
            category,
            folder,
            tags,
            description,
            favicon: this.getFaviconUrl(url),
            isFavorite,
            visitCount: existingBookmark ? existingBookmark.visitCount || 0 : 0,
            lastVisited: existingBookmark ? existingBookmark.lastVisited : null,
            isWorking: existingBookmark ? existingBookmark.isWorking : null,
            screenshot: existingBookmark ? existingBookmark.screenshot : null,
            metaData: existingBookmark ? existingBookmark.metaData : null,
            createdAt: existingBookmark ? existingBookmark.createdAt : new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        if (this.currentEditingId) {
            // Update existing bookmark
            const index = this.bookmarks.findIndex(b => b.id === this.currentEditingId);
            this.bookmarks[index] = bookmark;
            this.showNotification('বুকমার্ক আপডেট হয়েছে', 'success');
        } else {
            // Add new bookmark
            this.bookmarks.unshift(bookmark);
            this.showNotification('নতুন বুকমার্ক যোগ হয়েছে', 'success');
        }

        this.saveBookmarksToStorage();
        this.renderBookmarksInSearch();
        this.renderBookmarksList();
        this.updateStatistics();
        this.resetBookmarkForm();
    }

    // Delete current bookmark
    deleteBookmark() {
        if (this.currentEditingId && confirm('এই বুকমার্কটি মুছে ফেলতে চান?')) {
            this.deleteBookmarkById(this.currentEditingId);
            this.resetBookmarkForm();
        }
    }

    // Render bookmarks list in manager
    renderBookmarksList() {
        const container = document.getElementById('bookmarkList');
        if (!container) return;

        if (this.bookmarks.length === 0) {
            container.innerHTML = `
                <div class="bookmark-empty-state">
                    <i class="fas fa-bookmark"></i>
                    <h4>কোন বুকমার্ক নেই</h4>
                    <p>আপনার প্রথম বুকমার্ক যোগ করুন</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.bookmarks.map(bookmark => `
            <div class="bookmark-list-item ${this.currentEditingId === bookmark.id ? 'editing' : ''} ${bookmark.isFavorite ? 'favorite' : ''}" data-id="${bookmark.id}">
                <div class="bookmark-list-favicon">
                    ${bookmark.favicon ?
                        `<img src="${bookmark.favicon}" alt="favicon" onerror="this.style.display='none'">` :
                        bookmark.title.charAt(0).toUpperCase()
                    }
                </div>
                <div class="bookmark-list-content">
                    <div class="bookmark-list-title">
                        ${bookmark.isFavorite ? '<i class="fas fa-star favorite-star"></i>' : ''}
                        ${this.escapeHtml(bookmark.title)}
                    </div>
                    <div class="bookmark-list-url">${this.escapeHtml(bookmark.url)}</div>
                    <div class="bookmark-list-meta">
                        <span class="bookmark-list-category">${this.getCategoryName(bookmark.category)}</span>
                        ${bookmark.visitCount > 0 ? `<span class="bookmark-list-visits">${bookmark.visitCount} ভিজিট</span>` : ''}
                        ${bookmark.lastVisited ? `<span class="bookmark-list-last-visit">শেষ: ${this.formatDate(bookmark.lastVisited)}</span>` : ''}
                    </div>
                    <div class="bookmark-list-status">
                        <span class="status-indicator ${bookmark.isWorking === false ? 'broken' : bookmark.isWorking === true ? 'working' : 'checking'}"></span>
                        <span class="status-text">${bookmark.isWorking === false ? 'ব্রোকেন' : bookmark.isWorking === true ? 'কার্যকর' : 'অজানা'}</span>
                    </div>
                </div>
                <div class="bookmark-list-actions">
                    <button class="bookmark-list-action favorite ${bookmark.isFavorite ? 'active' : ''}" onclick="bookmarkManager.toggleFavorite('${bookmark.id}')" title="ফেভরিট">
                        <i class="fas fa-star"></i>
                    </button>
                    <button class="bookmark-list-action visit" onclick="bookmarkManager.visitBookmark('${bookmark.id}')" title="ভিজিট">
                        <i class="fas fa-external-link-alt"></i>
                    </button>
                    <button class="bookmark-list-action edit" onclick="bookmarkManager.editBookmark('${bookmark.id}')" title="সম্পাদনা">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="bookmark-list-action delete" onclick="bookmarkManager.confirmDeleteBookmark('${bookmark.id}')" title="মুছুন">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    // Export bookmarks
    exportBookmarks() {
        const data = {
            bookmarks: this.bookmarks,
            exportedAt: new Date().toISOString(),
            version: '1.0'
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `bookmarks-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);

        this.showNotification('বুকমার্ক এক্সপোর্ট হয়েছে', 'success');
    }

    // Import bookmarks
    importBookmarks(file) {
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);

                if (data.bookmarks && Array.isArray(data.bookmarks)) {
                    const importCount = data.bookmarks.length;

                    // Add imported bookmarks
                    data.bookmarks.forEach(bookmark => {
                        // Generate new ID to avoid conflicts
                        bookmark.id = this.generateId();
                        bookmark.importedAt = new Date().toISOString();
                    });

                    this.bookmarks = [...this.bookmarks, ...data.bookmarks];
                    this.saveBookmarksToStorage();
                    this.renderBookmarksInSearch();
                    this.renderBookmarksList();

                    this.showNotification(`${importCount} টি বুকমার্ক ইমপোর্ট হয়েছে`, 'success');
                } else {
                    throw new Error('Invalid file format');
                }
            } catch (error) {
                console.error('Import error:', error);
                alert('ফাইল ইমপোর্ট করতে সমস্যা হয়েছে। সঠিক JSON ফাইল নির্বাচন করুন।');
            }
        };
        reader.readAsText(file);
    }



    // Force save all bookmark data to localStorage
    forceSaveAllData() {
        this.saveBookmarksToStorage();
        this.saveFoldersToStorage();
        this.saveCategoriesToStorage();
        console.log('Forced save of all bookmark data completed');
    }

    // Load bookmarks from storage (public method)
    loadBookmarksFromStorage() {
        this.bookmarks = this.loadBookmarks();
        this.folders = this.loadFolders();
        this.categories = this.loadCategories();
        console.log('Reloaded bookmarks from storage:', this.bookmarks.length);
    }

    // Search bookmarks in the bookmark list (real-time search)
    searchBookmarksList(query) {
        const searchInput = document.getElementById('bookmarkListSearchInput');
        const clearBtn = document.getElementById('clearBookmarkListSearchBtn');

        // Show/hide clear button
        if (query.trim()) {
            clearBtn.style.display = 'flex';
        } else {
            clearBtn.style.display = 'none';
        }

        // Filter bookmarks based on search query
        let filteredBookmarks = this.bookmarks;

        if (query.trim()) {
            const searchTerm = query.toLowerCase();
            filteredBookmarks = this.bookmarks.filter(bookmark => {
                return bookmark.title.toLowerCase().includes(searchTerm) ||
                       bookmark.url.toLowerCase().includes(searchTerm) ||
                       (bookmark.description && bookmark.description.toLowerCase().includes(searchTerm)) ||
                       (bookmark.tags && bookmark.tags.some(tag => tag.toLowerCase().includes(searchTerm))) ||
                       this.getCategoryName(bookmark.category).toLowerCase().includes(searchTerm);
            });
        }

        // Render filtered bookmarks
        this.renderFilteredBookmarksList(filteredBookmarks);
    }

    // Clear bookmark list search
    clearBookmarkListSearch() {
        const searchInput = document.getElementById('bookmarkListSearchInput');
        const clearBtn = document.getElementById('clearBookmarkListSearchBtn');

        searchInput.value = '';
        clearBtn.style.display = 'none';

        // Show all bookmarks
        this.renderBookmarksList();
    }

    // Render filtered bookmarks list
    renderFilteredBookmarksList(filteredBookmarks) {
        const container = document.getElementById('bookmarkList');
        if (!container) return;

        if (filteredBookmarks.length === 0) {
            container.innerHTML = `
                <div class="bookmark-empty-state">
                    <i class="fas fa-search"></i>
                    <h4>কোন বুকমার্ক পাওয়া যায়নি</h4>
                    <p>অন্য কিওয়ার্ড দিয়ে খুঁজে দেখুন</p>
                </div>
            `;
            return;
        }

        container.innerHTML = filteredBookmarks.map(bookmark => `
            <div class="bookmark-list-item ${this.currentEditingId === bookmark.id ? 'editing' : ''} ${bookmark.isFavorite ? 'favorite' : ''}" data-id="${bookmark.id}">
                <div class="bookmark-list-favicon">
                    ${bookmark.favicon ?
                        `<img src="${bookmark.favicon}" alt="favicon" onerror="this.style.display='none'">` :
                        bookmark.title.charAt(0).toUpperCase()
                    }
                </div>
                <div class="bookmark-list-content">
                    <div class="bookmark-list-title">${this.escapeHtml(bookmark.title)}</div>
                    <div class="bookmark-list-url">${this.escapeHtml(bookmark.url)}</div>
                    <div class="bookmark-list-meta">
                        <span class="bookmark-category">${this.getCategoryName(bookmark.category)}</span>
                        ${bookmark.folder ? `<span class="bookmark-folder">${this.escapeHtml(bookmark.folder)}</span>` : ''}
                        <span class="bookmark-visits">ভিজিট: ${bookmark.visitCount || 0}</span>
                        <span class="bookmark-date">${new Date(bookmark.createdAt).toLocaleDateString('bn-BD')}</span>
                    </div>
                </div>
                <div class="bookmark-list-actions">
                    <button class="btn btn-icon-sm bookmark-action-btn" onclick="bookmarkManager.visitBookmark('${bookmark.id}')" title="ভিজিট করুন">
                        <i class="fas fa-external-link-alt"></i>
                    </button>
                    <button class="btn btn-icon-sm bookmark-action-btn ${bookmark.isFavorite ? 'favorite' : ''}" onclick="bookmarkManager.toggleFavorite('${bookmark.id}')" title="ফেভরিট">
                        <i class="fas fa-heart"></i>
                    </button>
                    <button class="btn btn-icon-sm bookmark-action-btn" onclick="bookmarkManager.editBookmark('${bookmark.id}')" title="সম্পাদনা">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-icon-sm bookmark-action-btn delete-btn" onclick="bookmarkManager.confirmDeleteBookmark('${bookmark.id}')" title="মুছুন">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }
}

// Global functions for modal management
function closeBookmarkModal() {
    if (window.bookmarkManager) {
        window.bookmarkManager.closeBookmarkModal();
    }
}

function closeSearchModal() {
    const modal = document.getElementById('searchModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }
}

// Initialize bookmark manager when DOM is loaded
let bookmarkManager;
document.addEventListener('DOMContentLoaded', () => {
    bookmarkManager = new BookmarkManager();
    window.bookmarkManager = bookmarkManager; // Make it globally accessible
});
