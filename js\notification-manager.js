/**
 * Notification Permission Management System
 * Handles browser notification permissions gracefully
 */

class NotificationManager {
    constructor() {
        this.permissionStatus = 'default';
        this.settings = {
            enabled: true,
            showPermissionDialog: true,
            autoRequest: false,
            reminderEnabled: true,
            soundEnabled: true
        };
        
        this.init();
    }

    init() {
        this.loadSettings();
        this.checkPermissionStatus();
        this.setupEventListeners();
    }

    // Load settings from localStorage
    loadSettings() {
        const saved = localStorage.getItem('notificationSettings');
        if (saved) {
            this.settings = { ...this.settings, ...JSON.parse(saved) };
        }
    }

    // Save settings to localStorage
    saveSettings() {
        localStorage.setItem('notificationSettings', JSON.stringify(this.settings));
    }

    // Check current permission status
    checkPermissionStatus() {
        if ('Notification' in window) {
            this.permissionStatus = Notification.permission;
        } else {
            this.permissionStatus = 'unsupported';
        }
        return this.permissionStatus;
    }

    // Setup event listeners
    setupEventListeners() {
        // Listen for permission changes
        if ('permissions' in navigator) {
            navigator.permissions.query({ name: 'notifications' }).then(permission => {
                permission.addEventListener('change', () => {
                    this.checkPermissionStatus();
                    this.updateUI();
                });
            }).catch(() => {
                // Fallback for browsers that don't support permissions API
                console.log('Permissions API not supported');
            });
        }
    }

    // Request notification permission with user-friendly dialog
    async requestPermission(showDialog = true) {
        // Check if notifications are supported
        if (!('Notification' in window)) {
            this.showUnsupportedMessage();
            return 'unsupported';
        }

        // If already granted, return immediately
        if (this.permissionStatus === 'granted') {
            return 'granted';
        }

        // If denied and user doesn't want to see dialog, return
        if (this.permissionStatus === 'denied' && !this.settings.showPermissionDialog) {
            return 'denied';
        }

        // Show custom dialog first if enabled
        if (showDialog && this.settings.showPermissionDialog) {
            const userChoice = await this.showPermissionDialog();
            if (!userChoice) {
                return 'denied';
            }
        }

        try {
            // Request permission
            const permission = await Notification.requestPermission();
            this.permissionStatus = permission;
            this.updateUI();
            
            if (permission === 'granted') {
                this.showSuccessMessage();
            } else if (permission === 'denied') {
                this.showDeniedMessage();
            }
            
            return permission;
        } catch (error) {
            console.error('Error requesting notification permission:', error);
            return 'denied';
        }
    }

    // Show custom permission dialog
    showPermissionDialog() {
        return new Promise((resolve) => {
            const modal = this.createPermissionModal();
            document.body.appendChild(modal);

            const allowBtn = modal.querySelector('.permission-allow');
            const denyBtn = modal.querySelector('.permission-deny');
            const dontAskBtn = modal.querySelector('.permission-dont-ask');

            allowBtn.onclick = () => {
                document.body.removeChild(modal);
                resolve(true);
            };

            denyBtn.onclick = () => {
                document.body.removeChild(modal);
                resolve(false);
            };

            dontAskBtn.onclick = () => {
                this.settings.showPermissionDialog = false;
                this.saveSettings();
                document.body.removeChild(modal);
                resolve(false);
            };

            // Close on overlay click
            modal.onclick = (e) => {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                    resolve(false);
                }
            };
        });
    }

    // Create permission dialog modal
    createPermissionModal() {
        const modal = document.createElement('div');
        modal.className = 'notification-permission-modal';
        modal.innerHTML = `
            <div class="permission-dialog">
                <div class="permission-header">
                    <i class="fas fa-bell"></i>
                    <h3>নোটিফিকেশন অনুমতি</h3>
                </div>
                <div class="permission-body">
                    <p>রিমাইন্ডার এবং গুরুত্বপূর্ণ আপডেটের জন্য নোটিফিকেশন পাঠাতে অনুমতি দিন।</p>
                    <div class="permission-benefits">
                        <div class="benefit-item">
                            <i class="fas fa-clock"></i>
                            <span>সময়মতো রিমাইন্ডার পাবেন</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-save"></i>
                            <span>অটো-সেভ নোটিফিকেশন</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>গুরুত্বপূর্ণ আপডেট</span>
                        </div>
                    </div>
                    <p class="permission-note">আপনি যেকোনো সময় সেটিংস থেকে এটি পরিবর্তন করতে পারবেন।</p>
                </div>
                <div class="permission-actions">
                    <button class="btn btn-primary permission-allow">
                        <i class="fas fa-check"></i>
                        অনুমতি দিন
                    </button>
                    <button class="btn btn-secondary permission-deny">
                        <i class="fas fa-times"></i>
                        এখন নয়
                    </button>
                    <button class="btn btn-link permission-dont-ask">
                        আর জিজ্ঞেস করবেন না
                    </button>
                </div>
            </div>
        `;
        return modal;
    }

    // Show success message
    showSuccessMessage() {
        if (window.showNotification) {
            window.showNotification(
                'নোটিফিকেশন চালু হয়েছে',
                'এখন আপনি রিমাইন্ডার এবং আপডেট পাবেন',
                'success'
            );
        }
    }

    // Show denied message
    showDeniedMessage() {
        if (window.showNotification) {
            window.showNotification(
                'নোটিফিকেশন বন্ধ',
                'রিমাইন্ডার কাজ করবে না। সেটিংস থেকে চালু করতে পারেন।',
                'warning'
            );
        }
    }

    // Show unsupported message
    showUnsupportedMessage() {
        if (window.showNotification) {
            window.showNotification(
                'নোটিফিকেশন সাপোর্ট নেই',
                'আপনার ব্রাউজার নোটিফিকেশন সাপোর্ট করে না',
                'error'
            );
        }
    }

    // Check if notifications can be shown
    canShowNotifications() {
        return this.settings.enabled && 
               this.permissionStatus === 'granted' && 
               'Notification' in window;
    }

    // Show notification if permission is granted
    async showNotification(title, message, options = {}) {
        if (!this.canShowNotifications()) {
            return false;
        }

        try {
            const notification = new Notification(title, {
                body: message,
                icon: options.icon || 'assets/icon.png',
                badge: options.badge || 'assets/icon.png',
                tag: options.tag || 'default',
                requireInteraction: options.requireInteraction || false,
                silent: !this.settings.soundEnabled,
                ...options
            });

            // Auto close after delay
            if (options.autoClose !== false) {
                setTimeout(() => {
                    notification.close();
                }, options.duration || 5000);
            }

            return notification;
        } catch (error) {
            console.error('Error showing notification:', error);
            return false;
        }
    }

    // Update UI based on permission status
    updateUI() {
        const statusElements = document.querySelectorAll('.notification-status');
        statusElements.forEach(element => {
            element.textContent = this.getStatusText();
            element.className = `notification-status status-${this.permissionStatus}`;
        });

        // Update settings UI if exists
        const enabledCheckbox = document.getElementById('notificationEnabled');
        if (enabledCheckbox) {
            enabledCheckbox.checked = this.settings.enabled;
        }

        const soundCheckbox = document.getElementById('notificationSound');
        if (soundCheckbox) {
            soundCheckbox.checked = this.settings.soundEnabled;
        }
    }

    // Get status text in Bengali
    getStatusText() {
        switch (this.permissionStatus) {
            case 'granted':
                return 'চালু আছে';
            case 'denied':
                return 'বন্ধ আছে';
            case 'default':
                return 'অনুমতি চাওয়া হয়নি';
            case 'unsupported':
                return 'সাপোর্ট নেই';
            default:
                return 'অজানা';
        }
    }

    // Enable/disable notifications
    setEnabled(enabled) {
        this.settings.enabled = enabled;
        this.saveSettings();
        this.updateUI();
    }

    // Enable/disable sound
    setSoundEnabled(enabled) {
        this.settings.soundEnabled = enabled;
        this.saveSettings();
    }

    // Reset permission dialog setting
    resetPermissionDialog() {
        this.settings.showPermissionDialog = true;
        this.saveSettings();
    }

    // Get settings for UI
    getSettings() {
        return { ...this.settings };
    }
}

// Create global instance
window.notificationManager = new NotificationManager();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationManager;
}
