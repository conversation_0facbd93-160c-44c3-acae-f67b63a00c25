// ===== ELECTRON AUDIO SYSTEM =====
// Enhanced notification sound system for Electron apps

class ElectronAudioManager {
    constructor() {
        this.isElectron = this.detectElectron();
        this.audioContext = null;
        this.audioBuffer = null;
        this.volume = 0.7;
        this.initialized = false;
        this.fallbackAudio = null;
        
        this.init();
    }
    
    // Detect if running in Electron
    detectElectron() {
        return typeof window !== 'undefined' && 
               window.process && 
               window.process.type === 'renderer';
    }
    
    // Initialize audio system
    async init() {
        try {
            // Method 1: Web Audio API (preferred)
            await this.initWebAudio();
            
            // Method 2: HTML5 Audio (fallback)
            this.initHTMLAudio();
            
            // Method 3: Electron native notifications (if available)
            if (this.isElectron) {
                this.initElectronAudio();
            }
            
            this.initialized = true;
            console.log('✓ Audio system initialized for Electron');
            
        } catch (error) {
            console.warn('Audio initialization failed:', error);
            this.initFallback();
        }
    }
    
    // Initialize Web Audio API
    async initWebAudio() {
        try {
            const AudioContext = window.AudioContext || window.webkitAudioContext;
            this.audioContext = new AudioContext();
            
            // Resume context if suspended (required for Chrome)
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }
            
            // Create notification sound buffer
            this.createNotificationBuffer();
            
        } catch (error) {
            console.warn('Web Audio API failed:', error);
            throw error;
        }
    }
    
    // Create audio buffer for notification sound
    createNotificationBuffer() {
        const sampleRate = this.audioContext.sampleRate;
        const duration = 0.3; // 300ms
        const frameCount = sampleRate * duration;
        
        this.audioBuffer = this.audioContext.createBuffer(1, frameCount, sampleRate);
        const channelData = this.audioBuffer.getChannelData(0);
        
        // Generate a pleasant notification sound (two-tone)
        for (let i = 0; i < frameCount; i++) {
            const t = i / sampleRate;
            const freq1 = 800; // First tone
            const freq2 = 1000; // Second tone
            
            let sample = 0;
            
            // First tone (0-150ms)
            if (t < 0.15) {
                sample = Math.sin(2 * Math.PI * freq1 * t) * Math.exp(-t * 3);
            }
            // Second tone (150-300ms)
            else {
                const t2 = t - 0.15;
                sample = Math.sin(2 * Math.PI * freq2 * t2) * Math.exp(-t2 * 5);
            }
            
            channelData[i] = sample * this.volume;
        }
    }
    
    // Initialize HTML5 Audio fallback
    initHTMLAudio() {
        try {
            this.fallbackAudio = new Audio();
            
            // Create a data URL for a simple beep sound
            const beepSound = this.generateBeepDataURL();
            this.fallbackAudio.src = beepSound;
            this.fallbackAudio.volume = this.volume;
            this.fallbackAudio.preload = 'auto';
            
        } catch (error) {
            console.warn('HTML5 Audio fallback failed:', error);
        }
    }
    
    // Initialize Electron-specific audio
    initElectronAudio() {
        try {
            // Try to access Electron's shell for system sounds
            if (window.require) {
                const { shell } = window.require('electron');
                this.electronShell = shell;
            }
        } catch (error) {
            console.warn('Electron audio access failed:', error);
        }
    }
    
    // Initialize basic fallback
    initFallback() {
        console.warn('Using basic fallback for audio');
        this.initialized = true;
    }
    
    // Generate beep sound as data URL
    generateBeepDataURL() {
        const sampleRate = 22050;
        const duration = 0.2;
        const samples = sampleRate * duration;
        const buffer = new ArrayBuffer(44 + samples * 2);
        const view = new DataView(buffer);
        
        // WAV header
        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };
        
        writeString(0, 'RIFF');
        view.setUint32(4, 36 + samples * 2, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true);
        view.setUint16(22, 1, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, sampleRate * 2, true);
        view.setUint16(32, 2, true);
        view.setUint16(34, 16, true);
        writeString(36, 'data');
        view.setUint32(40, samples * 2, true);
        
        // Generate beep samples
        for (let i = 0; i < samples; i++) {
            const t = i / sampleRate;
            const sample = Math.sin(2 * Math.PI * 800 * t) * Math.exp(-t * 3) * 0.3;
            view.setInt16(44 + i * 2, sample * 32767, true);
        }
        
        const blob = new Blob([buffer], { type: 'audio/wav' });
        return URL.createObjectURL(blob);
    }
    
    // Play notification sound
    async playNotification(type = 'default') {
        if (!this.initialized) {
            await this.init();
        }
        
        try {
            // Method 1: Web Audio API
            if (this.audioContext && this.audioBuffer) {
                await this.playWebAudio();
                return;
            }
            
            // Method 2: HTML5 Audio
            if (this.fallbackAudio) {
                await this.playHTMLAudio();
                return;
            }
            
            // Method 3: Electron system sound
            if (this.isElectron && this.electronShell) {
                this.playElectronSound();
                return;
            }
            
            // Method 4: Browser notification API
            this.playBrowserNotification();
            
        } catch (error) {
            console.warn('All audio methods failed:', error);
            // Final fallback: vibration (mobile) or console beep
            this.playFinalFallback();
        }
    }
    
    // Play using Web Audio API
    async playWebAudio() {
        if (this.audioContext.state === 'suspended') {
            await this.audioContext.resume();
        }
        
        const source = this.audioContext.createBufferSource();
        const gainNode = this.audioContext.createGain();
        
        source.buffer = this.audioBuffer;
        source.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        gainNode.gain.value = this.volume;
        
        source.start();
    }
    
    // Play using HTML5 Audio
    async playHTMLAudio() {
        this.fallbackAudio.currentTime = 0;
        await this.fallbackAudio.play();
    }
    
    // Play Electron system sound
    playElectronSound() {
        try {
            // Try to play system notification sound
            if (this.electronShell && this.electronShell.beep) {
                this.electronShell.beep();
            }
        } catch (error) {
            console.warn('Electron system sound failed:', error);
        }
    }
    
    // Play browser notification
    playBrowserNotification() {
        if ('Notification' in window && Notification.permission === 'granted') {
            const notification = new Notification('', {
                silent: false,
                requireInteraction: false
            });
            
            // Close immediately (we just want the sound)
            setTimeout(() => notification.close(), 100);
        }
    }
    
    // Final fallback methods
    playFinalFallback() {
        // Try vibration on mobile
        if (navigator.vibrate) {
            navigator.vibrate([200, 100, 200]);
        }
        
        // Console beep (some terminals support this)
        console.log('\x07');
        
        // Visual feedback
        document.body.style.backgroundColor = '#ffff00';
        setTimeout(() => {
            document.body.style.backgroundColor = '';
        }, 100);
    }
    
    // Set volume (0.0 to 1.0)
    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        if (this.fallbackAudio) {
            this.fallbackAudio.volume = this.volume;
        }
    }
    
    // Test audio system
    async test() {
        console.log('Testing audio system...');
        console.log('Is Electron:', this.isElectron);
        console.log('Audio Context:', !!this.audioContext);
        console.log('Audio Buffer:', !!this.audioBuffer);
        console.log('Fallback Audio:', !!this.fallbackAudio);
        
        await this.playNotification('test');
    }
}

// Global instance
window.electronAudio = new ElectronAudioManager();

// Electron IPC integration
class ElectronIPCHelper {
    constructor() {
        this.isElectron = this.detectElectron();
        this.ipcRenderer = null;

        if (this.isElectron) {
            try {
                this.ipcRenderer = window.require('electron').ipcRenderer;
            } catch (error) {
                console.warn('Failed to access Electron IPC:', error);
            }
        }
    }

    detectElectron() {
        return typeof window !== 'undefined' &&
               window.process &&
               window.process.type === 'renderer';
    }

    async playNotificationSound(type = 'default') {
        if (this.ipcRenderer) {
            try {
                return await this.ipcRenderer.invoke('play-notification-sound', type);
            } catch (error) {
                console.warn('IPC notification sound failed:', error);
                return false;
            }
        }
        return false;
    }

    async showSystemNotification(options) {
        if (this.ipcRenderer) {
            try {
                return await this.ipcRenderer.invoke('show-system-notification', options);
            } catch (error) {
                console.warn('IPC system notification failed:', error);
                return false;
            }
        }
        return false;
    }

    async setVolume(volume) {
        if (this.ipcRenderer) {
            try {
                return await this.ipcRenderer.invoke('set-notification-volume', volume);
            } catch (error) {
                console.warn('IPC volume control failed:', error);
                return false;
            }
        }
        return false;
    }
}

// Global IPC helper
window.electronIPC = new ElectronIPCHelper();

// Enhanced integration with existing notification system
if (typeof window.playNotificationSound === 'function') {
    const originalPlayNotificationSound = window.playNotificationSound;

    window.playNotificationSound = async function(type = 'default') {
        try {
            // Method 1: Try Electron IPC first (most reliable)
            if (window.electronIPC.isElectron) {
                const success = await window.electronIPC.playNotificationSound(type);
                if (success) return;
            }

            // Method 2: Try enhanced Web Audio
            await window.electronAudio.playNotification(type);

        } catch (error) {
            console.warn('Enhanced audio failed, using fallback:', error);
            // Method 3: Fallback to original function
            try {
                originalPlayNotificationSound(type);
            } catch (fallbackError) {
                console.error('All audio methods failed:', fallbackError);
            }
        }
    };
}

// Enhanced showNotification function for Electron
if (typeof window.showNotification === 'function') {
    const originalShowNotification = window.showNotification;

    window.showNotification = async function(title, message, type = 'info') {
        // Show web notification first
        originalShowNotification(title, message, type);

        // Also show system notification in Electron
        if (window.electronIPC.isElectron) {
            try {
                await window.electronIPC.showSystemNotification({
                    title: title,
                    message: message,
                    type: type,
                    autoClose: true,
                    duration: 3000
                });
            } catch (error) {
                console.warn('System notification failed:', error);
            }
        }
    };
}

// Audio test function for Electron
window.testElectronAudio = async function() {
    console.log('=== Testing Electron Audio System ===');
    console.log('Is Electron:', window.electronIPC.isElectron);
    console.log('IPC Available:', !!window.electronIPC.ipcRenderer);

    // Test all audio methods
    console.log('Testing IPC audio...');
    await window.electronIPC.playNotificationSound('test');

    console.log('Testing Web Audio...');
    await window.electronAudio.playNotification('test');

    console.log('Testing integrated function...');
    await window.playNotificationSound('test');

    console.log('Testing system notification...');
    await window.showNotification('টেস্ট', 'অডিও সিস্টেম টেস্ট', 'success');

    console.log('Audio test completed');
};

console.log('✓ Electron Audio Manager loaded');
console.log('✓ Electron IPC Helper loaded');
console.log('Run testElectronAudio() to test the audio system');
